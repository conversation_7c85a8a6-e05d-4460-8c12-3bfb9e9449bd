import { describe, expect, it } from "vitest";
import {
  formatInternationalPhone,
  isValidInternationalPhone
} from "./phone-utils"; // 替换成你的文件名

describe("isValidInternationalPhone - Parameterized Tests", () => {
  it.each([
    // ✅ 合法
    ["+8613812345678", true],
    ["******5550125", true],
    ["+44 7911123456", true],
    ["918888888888", true],
    ["+33123456789", true],
    ["819012345678", true],

    // ❌ 非法
    ["+86 12345", false],
    ["+86123456789012345", false],
    ["+86abc12345", false],
    ["++8613812345678", false],
    ["86_13812345678", false],
    ["   ", false],
    ["", false]
  ])("should return %s for input '%s'", (input, expected) => {
    expect(isValidInternationalPhone(input)).toBe(expected);
  });
});

describe("formatInternationalPhone", () => {
  it.each([
    ["+8613812345678", "+8613812345678"],
    ["8613812345678", "+8613812345678"],
    ["+44 7911 123456", "+447911123456"],
    ["******-555-0125", "+12025550125"]
  ])("should format '%s' to '%s'", (input, expected) => {
    expect(formatInternationalPhone(input)).toBe(expected);
  });
});
